# 大数据平台架构(综合实践) - Linux操作指南

## 一、环境准备

### 1.1 系统要求
- Linux系统（推荐Ubuntu 20.04 LTS或CentOS 7/8）
- 至少3台虚拟机或物理机
- 每台机器至少4GB内存，20GB硬盘空间
- 网络互通

### 1.2 主机规划
```
节点1 (master): *************  hadoop-master
节点2 (slave1): *************  hadoop-slave1  
节点3 (slave2): *************  hadoop-slave2
```

## 二、基础环境配置

### 2.1 配置主机名和hosts文件

在每台机器上执行：

```bash
# 设置主机名（分别在三台机器上执行）
sudo hostnamectl set-hostname hadoop-master    # 在master节点
sudo hostnamectl set-hostname hadoop-slave1    # 在slave1节点
sudo hostnamectl set-hostname hadoop-slave2    # 在slave2节点

# 编辑hosts文件（所有节点都要配置）
sudo vim /etc/hosts
```

在hosts文件中添加：
```
************* hadoop-master
************* hadoop-slave1
************* hadoop-slave2
```

### 2.2 创建hadoop用户

在所有节点执行：
```bash
# 创建hadoop用户
sudo useradd -m -s /bin/bash hadoop
sudo passwd hadoop

# 给hadoop用户sudo权限
sudo usermod -aG sudo hadoop

# 切换到hadoop用户
su - hadoop
```

### 2.3 配置SSH免密登录

在master节点执行：
```bash
# 生成SSH密钥
ssh-keygen -t rsa -P '' -f ~/.ssh/id_rsa

# 将公钥复制到所有节点（包括自己）
ssh-copy-id hadoop@hadoop-master
ssh-copy-id hadoop@hadoop-slave1
ssh-copy-id hadoop@hadoop-slave2

# 测试免密登录
ssh hadoop-slave1
ssh hadoop-slave2
```

### 2.4 安装Java环境

在所有节点执行：
```bash
# 更新包管理器
sudo apt update  # Ubuntu
# 或
sudo yum update  # CentOS

# 安装OpenJDK 8
sudo apt install openjdk-8-jdk  # Ubuntu
# 或
sudo yum install java-1.8.0-openjdk java-1.8.0-openjdk-devel  # CentOS

# 配置JAVA_HOME
echo 'export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64' >> ~/.bashrc  # Ubuntu
# 或
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc  # CentOS

source ~/.bashrc

# 验证Java安装
java -version
```

## 三、Hadoop安装与配置

### 3.1 下载和安装Hadoop

在master节点执行：
```bash
# 下载Hadoop
cd /home/<USER>
wget https://archive.apache.org/dist/hadoop/common/hadoop-3.3.4/hadoop-3.3.4.tar.gz

# 解压
tar -xzf hadoop-3.3.4.tar.gz
mv hadoop-3.3.4 hadoop

# 配置环境变量
echo 'export HADOOP_HOME=/home/<USER>/hadoop' >> ~/.bashrc
echo 'export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop' >> ~/.bashrc
echo 'export PATH=$PATH:$HADOOP_HOME/bin:$HADOOP_HOME/sbin' >> ~/.bashrc
source ~/.bashrc
```

### 3.2 配置Hadoop

#### 3.2.1 配置hadoop-env.sh
```bash
vim $HADOOP_HOME/etc/hadoop/hadoop-env.sh
```

添加或修改：
```bash
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64  # Ubuntu
# 或
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk    # CentOS
```

#### 3.2.2 配置core-site.xml
```bash
vim $HADOOP_HOME/etc/hadoop/core-site.xml
```

内容：
```xml
<configuration>
    <property>
        <name>fs.defaultFS</name>
        <value>hdfs://hadoop-master:9000</value>
    </property>
    <property>
        <name>hadoop.tmp.dir</name>
        <value>/home/<USER>/hadoop/tmp</value>
    </property>
</configuration>
```

#### 3.2.3 配置hdfs-site.xml
```bash
vim $HADOOP_HOME/etc/hadoop/hdfs-site.xml
```

内容：
```xml
<configuration>
    <property>
        <name>dfs.replication</name>
        <value>2</value>
    </property>
    <property>
        <name>dfs.namenode.name.dir</name>
        <value>/home/<USER>/hadoop/dfs/name</value>
    </property>
    <property>
        <name>dfs.datanode.data.dir</name>
        <value>/home/<USER>/hadoop/dfs/data</value>
    </property>
    <property>
        <name>dfs.namenode.http-address</name>
        <value>hadoop-master:9870</value>
    </property>
</configuration>
```

#### 3.2.4 配置mapred-site.xml
```bash
vim $HADOOP_HOME/etc/hadoop/mapred-site.xml
```

内容：
```xml
<configuration>
    <property>
        <name>mapreduce.framework.name</name>
        <value>yarn</value>
    </property>
    <property>
        <name>mapreduce.application.classpath</name>
        <value>$HADOOP_MAPRED_HOME/share/hadoop/mapreduce/*:$HADOOP_MAPRED_HOME/share/hadoop/mapreduce/lib/*</value>
    </property>
</configuration>
```

#### 3.2.5 配置yarn-site.xml
```bash
vim $HADOOP_HOME/etc/hadoop/yarn-site.xml
```

内容：
```xml
<configuration>
    <property>
        <name>yarn.nodemanager.aux-services</name>
        <value>mapreduce_shuffle</value>
    </property>
    <property>
        <name>yarn.resourcemanager.hostname</name>
        <value>hadoop-master</value>
    </property>
</configuration>
```

#### 3.2.6 配置workers文件
```bash
vim $HADOOP_HOME/etc/hadoop/workers
```

内容：
```
hadoop-slave1
hadoop-slave2
```

### 3.3 分发Hadoop到其他节点

在master节点执行：
```bash
# 将hadoop目录复制到其他节点
scp -r /home/<USER>/hadoop hadoop@hadoop-slave1:/home/<USER>/
scp -r /home/<USER>/hadoop hadoop@hadoop-slave2:/home/<USER>/

# 复制环境变量配置
scp ~/.bashrc hadoop@hadoop-slave1:~/
scp ~/.bashrc hadoop@hadoop-slave2:~/
```

在slave节点执行：
```bash
source ~/.bashrc
```

## 四、启动Hadoop集群

### 4.1 格式化NameNode

在master节点执行：
```bash
# 创建必要目录
mkdir -p /home/<USER>/hadoop/tmp
mkdir -p /home/<USER>/hadoop/dfs/name
mkdir -p /home/<USER>/hadoop/dfs/data

# 格式化NameNode（只需执行一次）
hdfs namenode -format
```

### 4.2 启动集群

在master节点执行：
```bash
# 启动HDFS
start-dfs.sh

# 启动YARN
start-yarn.sh

# 检查进程
jps
```

### 4.3 验证集群状态

```bash
# 查看HDFS状态
hdfs dfsadmin -report

# 查看集群节点
yarn node -list

# Web界面访问
# NameNode: http://hadoop-master:9870
# ResourceManager: http://hadoop-master:8088
```

## 五、数据操作

### 5.1 创建HDFS目录和上传数据

```bash
# 创建目录
hdfs dfs -mkdir /data
hdfs dfs -mkdir /data/input

# 上传data.csv文件
hdfs dfs -put data.csv /data/input/

# 查看文件
hdfs dfs -ls /data/input/
hdfs dfs -cat /data/input/data.csv | head -10
```

### 5.2 HDFS基本操作命令

```bash
# 查看目录
hdfs dfs -ls /

# 创建目录
hdfs dfs -mkdir /test

# 上传文件
hdfs dfs -put local_file.txt /test/

# 下载文件
hdfs dfs -get /test/local_file.txt ./

# 删除文件
hdfs dfs -rm /test/local_file.txt

# 删除目录
hdfs dfs -rm -r /test

# 查看文件内容
hdfs dfs -cat /data/input/data.csv

# 查看文件大小
hdfs dfs -du -h /data/input/
```

## 六、集群管理

### 6.1 启动和停止集群

```bash
# 启动集群
start-dfs.sh
start-yarn.sh

# 停止集群
stop-yarn.sh
stop-dfs.sh

# 重启集群
stop-all.sh
start-all.sh
```

### 6.2 监控和日志

```bash
# 查看日志
tail -f $HADOOP_HOME/logs/hadoop-hadoop-namenode-hadoop-master.log

# 查看集群健康状态
hdfs fsck /

# 查看块信息
hdfs fsck /data/input/data.csv -files -blocks -locations
```

## 七、故障排除

### 7.1 常见问题

1. **无法启动NameNode**
   ```bash
   # 检查日志
   cat $HADOOP_HOME/logs/hadoop-hadoop-namenode-*.log
   
   # 重新格式化（注意：会删除所有数据）
   hdfs namenode -format -force
   ```

2. **DataNode无法连接**
   ```bash
   # 检查网络连通性
   ping hadoop-slave1
   
   # 检查防火墙
   sudo ufw status  # Ubuntu
   sudo firewall-cmd --state  # CentOS
   ```

3. **权限问题**
   ```bash
   # 修改目录权限
   sudo chown -R hadoop:hadoop /home/<USER>/hadoop
   ```

### 7.2 性能优化

```bash
# 调整JVM内存（在hadoop-env.sh中）
export HADOOP_NAMENODE_OPTS="-Xmx2g"
export HADOOP_DATANODE_OPTS="-Xmx1g"
```

## 八、下一步操作

1. 确保Hadoop集群正常运行
2. 将data.csv上传到HDFS
3. 在本地PyCharm中连接HDFS进行数据分析
4. 使用ECharts创建可视化大屏

完成以上步骤后，您的Hadoop集群就可以为数据分析提供分布式存储支持了。
