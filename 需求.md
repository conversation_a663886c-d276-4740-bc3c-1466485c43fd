一、综合实践任务介绍
H2
任务名称:大数据平台架构(综合实践)
任务内容:实现数据采集一分析一部署过程，要求结合Hadoop、爬虫、Python数据分析、机器学习、可视化展示完成综合实践任务
、技术实施要求
1.技术栈整合:
。爬虫(Scrapy/BeautifulSoup)→Hadoop(HDFS)→Pandas/NumPy→机器学习(Scikitlearn/TensorFlow)一可视化(ECharts/FineBl)一部署(Flask/Django + Nginx)。
2.算法深度:
。每个项目需包含至少2个机器学习算法，需说明算法选型理由，算法型评估。3.工程化能力:
数据管道自动化(爬虫定时任务→Hadoop自动入库)。
虚拟机/服务器部署文档(Docker容器化)
模块
数据采集
技术要求
爬虫爬取>5万条有效数据
验收标准
数据完整性>95%，字段缺失率<5%
反爬策略:IP代理/请求头轮换
Hadoop
数据分析
HDFS分区存储原始数据
Pandas预处理:缺失值填充/异常值过滤
Hadoop 分布式环境，至少3节点。
输出数据分析报告(含可视化图表)
统计指标:相关系数、分布直方图、Top-N排名
机器学习
系统部署
至少实现2类机器算法(如聚类+预测)
第三方库:Scikit-learn/Surprise/XGBoost
Web框架:Flask/Django提供API接口
模型评估报告(准确率/召回率/F1值)+特:
性分析
服务可访问，API响应时间≤500ms