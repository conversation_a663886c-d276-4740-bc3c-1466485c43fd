# 大数据平台架构(综合实践) - Ubuntu系统专用指南

## 一、Ubuntu环境准备

### 1.1 系统要求
- Ubuntu 20.04 LTS 或 Ubuntu 22.04 LTS（推荐）
- 至少3台Ubuntu机器（虚拟机或物理机）
- 每台机器至少4GB内存，20GB硬盘空间
- 网络互通

### 1.2 主机规划
```
节点1 (master): *************  hadoop-master
节点2 (slave1): *************  hadoop-slave1  
节点3 (slave2): *************  hadoop-slave2
```

## 二、Ubuntu基础环境配置

### 2.1 更新系统包
在所有节点执行：
```bash
# 更新包列表
sudo apt update

# 升级系统包
sudo apt upgrade -y

# 安装必要工具
sudo apt install -y vim wget curl net-tools openssh-server
```

### 2.2 配置主机名和hosts文件
```bash
# 设置主机名（分别在三台机器上执行）
sudo hostnamectl set-hostname hadoop-master    # 在master节点
sudo hostnamectl set-hostname hadoop-slave1    # 在slave1节点
sudo hostnamectl set-hostname hadoop-slave2    # 在slave2节点

# 验证主机名
hostnamectl

# 编辑hosts文件（所有节点都要配置）
sudo vim /etc/hosts
```

在hosts文件中添加（在127.0.0.1 localhost行下方添加）：
```
************* hadoop-master
************* hadoop-slave1
************* hadoop-slave2
```

### 2.3 创建hadoop用户
```bash
# 创建hadoop用户
sudo adduser hadoop

# 将hadoop用户添加到sudo组
sudo usermod -aG sudo hadoop

# 切换到hadoop用户
su - hadoop
```

### 2.4 配置SSH服务
```bash
# 确保SSH服务运行
sudo systemctl enable ssh
sudo systemctl start ssh
sudo systemctl status ssh

# 配置SSH免密登录（在master节点执行）
ssh-keygen -t rsa -P '' -f ~/.ssh/id_rsa

# 将公钥复制到所有节点
ssh-copy-id hadoop@hadoop-master
ssh-copy-id hadoop@hadoop-slave1
ssh-copy-id hadoop@hadoop-slave2

# 测试免密登录
ssh hadoop-slave1 'hostname'
ssh hadoop-slave2 'hostname'
```

### 2.5 安装Java环境
```bash
# 安装OpenJDK 8
sudo apt install -y openjdk-8-jdk

# 验证Java安装
java -version
javac -version

# 查找JAVA_HOME路径
sudo find /usr -name "java-8-openjdk*" -type d 2>/dev/null

# 配置JAVA_HOME环境变量
echo 'export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64' >> ~/.bashrc
echo 'export PATH=$PATH:$JAVA_HOME/bin' >> ~/.bashrc
source ~/.bashrc

# 验证JAVA_HOME
echo $JAVA_HOME
```

## 三、Ubuntu防火墙配置

### 3.1 配置UFW防火墙
```bash
# 检查防火墙状态
sudo ufw status

# 如果防火墙是活动的，需要开放Hadoop端口
sudo ufw allow 9000    # HDFS NameNode
sudo ufw allow 9870    # HDFS NameNode Web UI
sudo ufw allow 8088    # YARN ResourceManager Web UI
sudo ufw allow 19888   # MapReduce JobHistory Server
sudo ufw allow 8042    # YARN NodeManager Web UI
sudo ufw allow 22      # SSH

# 或者在测试环境中可以临时关闭防火墙
sudo ufw disable
```

## 四、Hadoop安装与配置（Ubuntu优化版）

### 4.1 下载和安装Hadoop
```bash
# 切换到hadoop用户
su - hadoop

# 创建下载目录
mkdir -p ~/downloads
cd ~/downloads

# 下载Hadoop（使用清华大学镜像，速度更快）
wget https://mirrors.tuna.tsinghua.edu.cn/apache/hadoop/common/hadoop-3.3.4/hadoop-3.3.4.tar.gz

# 验证下载完整性（可选）
wget https://mirrors.tuna.tsinghua.edu.cn/apache/hadoop/common/hadoop-3.3.4/hadoop-3.3.4.tar.gz.sha512
sha512sum -c hadoop-3.3.4.tar.gz.sha512

# 解压到用户目录
cd ~
tar -xzf ~/downloads/hadoop-3.3.4.tar.gz
mv hadoop-3.3.4 hadoop

# 设置目录权限
chmod 755 ~/hadoop
```

### 4.2 配置Hadoop环境变量
```bash
# 配置Hadoop环境变量
cat >> ~/.bashrc << 'EOF'

# Hadoop Environment Variables
export HADOOP_HOME=/home/<USER>/hadoop
export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop
export HADOOP_MAPRED_HOME=$HADOOP_HOME
export HADOOP_COMMON_HOME=$HADOOP_HOME
export HADOOP_HDFS_HOME=$HADOOP_HOME
export YARN_HOME=$HADOOP_HOME
export PATH=$PATH:$HADOOP_HOME/bin:$HADOOP_HOME/sbin
EOF

# 重新加载环境变量
source ~/.bashrc

# 验证Hadoop安装
hadoop version
```

### 4.3 创建Hadoop工作目录
```bash
# 创建必要的目录
mkdir -p ~/hadoop/logs
mkdir -p ~/hadoop/tmp
mkdir -p ~/hadoop/dfs/name
mkdir -p ~/hadoop/dfs/data

# 设置目录权限
chmod 755 ~/hadoop/tmp
chmod 755 ~/hadoop/dfs
chmod 755 ~/hadoop/dfs/name
chmod 755 ~/hadoop/dfs/data
```

### 4.4 配置Hadoop配置文件

#### 4.4.1 配置hadoop-env.sh
```bash
# 编辑hadoop-env.sh
vim $HADOOP_HOME/etc/hadoop/hadoop-env.sh

# 找到JAVA_HOME行并修改为：
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64

# 添加以下配置以避免警告
export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop
export HADOOP_OS_TYPE=${HADOOP_OS_TYPE:-$(uname -s)}
```

#### 4.4.2 配置core-site.xml
```bash
vim $HADOOP_HOME/etc/hadoop/core-site.xml
```

内容：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration>
    <property>
        <name>fs.defaultFS</name>
        <value>hdfs://hadoop-master:9000</value>
        <description>The default file system URI</description>
    </property>
    <property>
        <name>hadoop.tmp.dir</name>
        <value>/home/<USER>/hadoop/tmp</value>
        <description>Temporary directory for Hadoop</description>
    </property>
    <property>
        <name>hadoop.http.staticuser.user</name>
        <value>hadoop</value>
    </property>
</configuration>
```

#### 4.4.3 配置hdfs-site.xml
```bash
vim $HADOOP_HOME/etc/hadoop/hdfs-site.xml
```

内容：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration>
    <property>
        <name>dfs.replication</name>
        <value>2</value>
        <description>Default block replication</description>
    </property>
    <property>
        <name>dfs.namenode.name.dir</name>
        <value>/home/<USER>/hadoop/dfs/name</value>
        <description>Path on the local filesystem where the NameNode stores the namespace and transaction logs</description>
    </property>
    <property>
        <name>dfs.datanode.data.dir</name>
        <value>/home/<USER>/hadoop/dfs/data</value>
        <description>Comma separated list of paths on the local filesystem of a DataNode where it should store its blocks</description>
    </property>
    <property>
        <name>dfs.namenode.http-address</name>
        <value>hadoop-master:9870</value>
        <description>The address and the base port where the dfs namenode web ui will listen on</description>
    </property>
    <property>
        <name>dfs.permissions.enabled</name>
        <value>false</value>
        <description>If "true", enable permission checking in HDFS</description>
    </property>
</configuration>
```

#### 4.4.4 配置mapred-site.xml
```bash
vim $HADOOP_HOME/etc/hadoop/mapred-site.xml
```

内容：
```xml
<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration>
    <property>
        <name>mapreduce.framework.name</name>
        <value>yarn</value>
        <description>The runtime framework for executing MapReduce jobs</description>
    </property>
    <property>
        <name>mapreduce.application.classpath</name>
        <value>$HADOOP_MAPRED_HOME/share/hadoop/mapreduce/*:$HADOOP_MAPRED_HOME/share/hadoop/mapreduce/lib/*</value>
    </property>
    <property>
        <name>mapreduce.jobhistory.address</name>
        <value>hadoop-master:10020</value>
    </property>
    <property>
        <name>mapreduce.jobhistory.webapp.address</name>
        <value>hadoop-master:19888</value>
    </property>
</configuration>
```

#### 4.4.5 配置yarn-site.xml
```bash
vim $HADOOP_HOME/etc/hadoop/yarn-site.xml
```

内容：
```xml
<?xml version="1.0"?>
<configuration>
    <property>
        <name>yarn.nodemanager.aux-services</name>
        <value>mapreduce_shuffle</value>
        <description>Shuffle service that needs to be set for Map Reduce applications</description>
    </property>
    <property>
        <name>yarn.resourcemanager.hostname</name>
        <value>hadoop-master</value>
        <description>The hostname of the ResourceManager</description>
    </property>
    <property>
        <name>yarn.resourcemanager.webapp.address</name>
        <value>hadoop-master:8088</value>
    </property>
    <property>
        <name>yarn.nodemanager.webapp.address</name>
        <value>0.0.0.0:8042</value>
    </property>
    <property>
        <name>yarn.nodemanager.resource.memory-mb</name>
        <value>2048</value>
        <description>Amount of physical memory, in MB, that can be allocated for containers</description>
    </property>
    <property>
        <name>yarn.scheduler.maximum-allocation-mb</name>
        <value>2048</value>
    </property>
    <property>
        <name>yarn.scheduler.minimum-allocation-mb</name>
        <value>128</value>
    </property>
    <property>
        <name>yarn.nodemanager.vmem-check-enabled</name>
        <value>false</value>
        <description>Whether virtual memory limits will be enforced for containers</description>
    </property>
</configuration>
```

#### 4.4.6 配置workers文件
```bash
vim $HADOOP_HOME/etc/hadoop/workers

# 删除localhost，添加以下内容：
hadoop-slave1
hadoop-slave2
```

## 五、分发Hadoop到其他节点

### 5.1 使用rsync分发（推荐）
```bash
# 在master节点执行
# 安装rsync（如果没有）
sudo apt install -y rsync

# 分发hadoop目录到slave节点
rsync -avz ~/hadoop/ hadoop@hadoop-slave1:~/hadoop/
rsync -avz ~/hadoop/ hadoop@hadoop-slave2:~/hadoop/

# 分发环境变量配置
scp ~/.bashrc hadoop@hadoop-slave1:~/
scp ~/.bashrc hadoop@hadoop-slave2:~/
```

### 5.2 在slave节点配置环境
```bash
# 在每个slave节点执行
ssh hadoop-slave1
source ~/.bashrc
mkdir -p ~/hadoop/logs ~/hadoop/tmp ~/hadoop/dfs/name ~/hadoop/dfs/data
exit

ssh hadoop-slave2
source ~/.bashrc
mkdir -p ~/hadoop/logs ~/hadoop/tmp ~/hadoop/dfs/name ~/hadoop/dfs/data
exit
```

## 六、启动Hadoop集群

### 6.1 格式化NameNode
```bash
# 在master节点执行（只需执行一次）
hdfs namenode -format -force

# 看到 "Storage directory /home/<USER>/hadoop/dfs/name has been successfully formatted" 表示成功
```

### 6.2 启动集群
```bash
# 启动HDFS
start-dfs.sh

# 启动YARN
start-yarn.sh

# 或者一次性启动所有服务
# start-all.sh
```

### 6.3 验证集群状态
```bash
# 检查Java进程
jps

# 在master节点应该看到：
# - NameNode
# - SecondaryNameNode
# - ResourceManager

# 在slave节点应该看到：
# - DataNode
# - NodeManager

# 检查HDFS状态
hdfs dfsadmin -report

# 检查集群节点
yarn node -list

# 检查HDFS健康状态
hdfs fsck /
```

### 6.4 Web界面访问
```bash
# 在浏览器中访问以下地址：
# NameNode Web UI: http://hadoop-master:9870
# ResourceManager Web UI: http://hadoop-master:8088

# 如果无法访问，检查防火墙设置
sudo ufw status
```

## 七、Ubuntu系统优化建议

### 7.1 系统性能优化
```bash
# 增加文件描述符限制
echo "hadoop soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "hadoop hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 禁用swap（可选，提高性能）
sudo swapoff -a
# 永久禁用需要编辑 /etc/fstab

# 调整内核参数
echo "vm.swappiness=1" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 7.2 监控和日志
```bash
# 查看系统资源使用情况
htop
# 如果没有安装：sudo apt install htop

# 查看磁盘使用情况
df -h

# 查看Hadoop日志
tail -f $HADOOP_HOME/logs/hadoop-hadoop-namenode-*.log
tail -f $HADOOP_HOME/logs/hadoop-hadoop-datanode-*.log
```

## 八、常见Ubuntu问题解决

### 8.1 权限问题
```bash
# 如果遇到权限问题
sudo chown -R hadoop:hadoop /home/<USER>/hadoop
chmod -R 755 /home/<USER>/hadoop
```

### 8.2 网络问题
```bash
# 测试网络连通性
ping hadoop-slave1
ping hadoop-slave2

# 检查端口是否开放
netstat -tlnp | grep :9000
netstat -tlnp | grep :9870
```

### 8.3 Java相关问题
```bash
# 如果Java版本不对
sudo update-alternatives --config java

# 重新设置JAVA_HOME
export JAVA_HOME=$(readlink -f /usr/bin/java | sed "s:bin/java::")
```

## 九、Ubuntu上的数据操作

### 9.1 上传data.csv到HDFS
```bash
# 创建HDFS目录结构
hdfs dfs -mkdir -p /bigdata-project
hdfs dfs -mkdir -p /bigdata-project/raw-data
hdfs dfs -mkdir -p /bigdata-project/processed-data
hdfs dfs -mkdir -p /bigdata-project/analysis-results

# 检查data.csv文件是否存在
ls -la data.csv

# 如果文件不存在，创建示例数据文件
if [ ! -f "data.csv" ]; then
    echo "创建示例数据文件..."
    cat > data.csv << 'EOF'
id,name,age,salary,department,performance_score
1,张三,25,5000,IT,85
2,李四,30,7000,HR,92
3,王五,28,6000,Finance,78
4,赵六,35,8000,IT,88
5,钱七,26,5500,Marketing,90
6,孙八,32,7500,IT,82
7,周九,29,6500,HR,87
8,吴十,31,7200,Finance,91
9,郑一,27,5800,Marketing,86
10,王二,33,7800,IT,89
EOF
    echo "示例数据文件已创建"
fi

# 上传到HDFS
hdfs dfs -put data.csv /bigdata-project/raw-data/

# 验证上传
hdfs dfs -ls /bigdata-project/raw-data/
hdfs dfs -cat /bigdata-project/raw-data/data.csv | head -5
```

### 9.2 HDFS基本操作
```bash
# 查看HDFS使用情况
hdfs dfsadmin -report

# 查看文件详细信息
hdfs dfs -stat "%n %o %r %u %g %y %b" /bigdata-project/raw-data/data.csv

# 检查文件完整性
hdfs fsck /bigdata-project/raw-data/data.csv -files -blocks -locations
```

## 十、Ubuntu上的Python环境配置

### 10.1 安装Python和pip
```bash
# Ubuntu通常已预装Python3，检查版本
python3 --version
pip3 --version

# 如果没有pip，安装它
sudo apt update
sudo apt install -y python3-pip python3-venv python3-dev

# 安装系统级依赖
sudo apt install -y build-essential libssl-dev libffi-dev
```

### 10.2 创建Python项目环境
```bash
# 创建项目目录
mkdir -p ~/bigdata-analysis
cd ~/bigdata-analysis

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 安装数据分析包
pip install pandas numpy matplotlib seaborn jupyter

# 安装Web框架
pip install flask

# 安装HDFS客户端（Ubuntu优化版本）
pip install hdfs3 requests

# 生成requirements.txt
pip freeze > requirements.txt
```

### 10.3 创建Ubuntu优化的HDFS连接配置
```bash
# 创建配置目录
mkdir -p config utils analysis templates

# 创建HDFS配置文件
cat > config/hdfs_config.py << 'EOF'
import os

# HDFS配置 - Ubuntu优化版
HDFS_CONFIG = {
    'namenode_host': 'hadoop-master',  # 或使用IP
    'namenode_port': 9000,
    'webhdfs_port': 9870,
    'user': 'hadoop',
    'timeout': 30
}

# HDFS路径配置
HDFS_PATHS = {
    'raw_data': '/bigdata-project/raw-data/',
    'processed_data': '/bigdata-project/processed-data/',
    'analysis_results': '/bigdata-project/analysis-results/'
}

# Ubuntu系统路径
LOCAL_PATHS = {
    'charts': './charts/',
    'results': './results/',
    'logs': './logs/'
}

# 确保本地目录存在
for path in LOCAL_PATHS.values():
    os.makedirs(path, exist_ok=True)
EOF
```

### 10.4 创建Ubuntu优化的HDFS工具类
```bash
cat > utils/hdfs_utils.py << 'EOF'
import pandas as pd
import requests
import json
import os
from urllib.parse import quote
from config.hdfs_config import HDFS_CONFIG, HDFS_PATHS, LOCAL_PATHS

class HDFSManager:
    def __init__(self):
        self.base_url = f"http://{HDFS_CONFIG['namenode_host']}:{HDFS_CONFIG['webhdfs_port']}/webhdfs/v1"
        self.user = HDFS_CONFIG['user']
        self.timeout = HDFS_CONFIG['timeout']

    def _make_request(self, method, path, params=None, data=None):
        """发送HTTP请求到WebHDFS"""
        url = f"{self.base_url}{path}"
        default_params = {'user.name': self.user}
        if params:
            default_params.update(params)

        try:
            response = requests.request(
                method, url, params=default_params,
                data=data, timeout=self.timeout
            )
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            print(f"HDFS请求失败: {e}")
            raise

    def read_csv_from_hdfs(self, filename):
        """从HDFS读取CSV文件"""
        hdfs_path = HDFS_PATHS['raw_data'] + filename
        print(f"正在从HDFS读取文件: {hdfs_path}")

        try:
            # 使用WebHDFS API读取文件
            response = self._make_request('GET', hdfs_path, {'op': 'OPEN'})

            # 保存到本地临时文件
            temp_file = f"{LOCAL_PATHS['results']}{filename}"
            with open(temp_file, 'wb') as f:
                f.write(response.content)

            # 读取为DataFrame
            df = pd.read_csv(temp_file)
            print(f"成功读取数据，形状: {df.shape}")
            return df

        except Exception as e:
            print(f"读取HDFS文件失败: {e}")
            # 如果HDFS读取失败，尝试读取本地文件
            if os.path.exists(filename):
                print(f"使用本地文件: {filename}")
                return pd.read_csv(filename)
            raise

    def save_csv_to_hdfs(self, df, filename, path_type='processed_data'):
        """保存DataFrame到HDFS"""
        hdfs_path = HDFS_PATHS[path_type] + filename
        local_temp = f"{LOCAL_PATHS['results']}{filename}"

        try:
            # 先保存到本地
            df.to_csv(local_temp, index=False)

            # 上传到HDFS
            with open(local_temp, 'rb') as f:
                self._make_request('PUT', hdfs_path,
                                 {'op': 'CREATE', 'overwrite': 'true'},
                                 data=f.read())

            print(f"文件已保存到HDFS: {hdfs_path}")

        except Exception as e:
            print(f"保存到HDFS失败: {e}")
            print(f"文件已保存到本地: {local_temp}")

    def list_files(self, path_type='raw_data'):
        """列出HDFS目录中的文件"""
        try:
            hdfs_path = HDFS_PATHS[path_type]
            response = self._make_request('GET', hdfs_path, {'op': 'LISTSTATUS'})
            data = response.json()
            files = [item['pathSuffix'] for item in data['FileStatuses']['FileStatus']]
            return files
        except Exception as e:
            print(f"列出HDFS文件失败: {e}")
            return []

    def file_exists(self, filename, path_type='raw_data'):
        """检查文件是否存在"""
        try:
            hdfs_path = HDFS_PATHS[path_type] + filename
            response = self._make_request('GET', hdfs_path, {'op': 'GETFILESTATUS'})
            return response.status_code == 200
        except:
            return False

    def test_connection(self):
        """测试HDFS连接"""
        try:
            response = self._make_request('GET', '/', {'op': 'LISTSTATUS'})
            print("✅ HDFS连接成功")
            return True
        except Exception as e:
            print(f"❌ HDFS连接失败: {e}")
            return False
EOF
```

## 十一、Ubuntu系统测试和验证

### 11.1 测试Hadoop集群
```bash
# 创建测试脚本
cat > test_hadoop.sh << 'EOF'
#!/bin/bash

echo "🔍 测试Hadoop集群状态..."

# 检查Java进程
echo "1. 检查Java进程:"
jps

echo -e "\n2. 检查HDFS状态:"
hdfs dfsadmin -report | head -20

echo -e "\n3. 检查YARN节点:"
yarn node -list

echo -e "\n4. 检查HDFS健康状态:"
hdfs fsck / | head -10

echo -e "\n5. 测试HDFS读写:"
echo "test data" > test.txt
hdfs dfs -put test.txt /
hdfs dfs -cat /test.txt
hdfs dfs -rm /test.txt
rm test.txt

echo -e "\n✅ Hadoop集群测试完成"
EOF

chmod +x test_hadoop.sh
./test_hadoop.sh
```

### 11.2 测试Python环境
```bash
# 激活虚拟环境
source venv/bin/activate

# 创建Python测试脚本
cat > test_python_env.py << 'EOF'
#!/usr/bin/env python3

import sys
import subprocess

def test_imports():
    """测试Python包导入"""
    packages = [
        'pandas', 'numpy', 'matplotlib', 'seaborn',
        'flask', 'requests', 'json'
    ]

    print("🔍 测试Python包导入...")
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError as e:
            print(f"❌ {package}: {e}")

def test_hdfs_connection():
    """测试HDFS连接"""
    print("\n🔍 测试HDFS连接...")
    try:
        from utils.hdfs_utils import HDFSManager
        hdfs_manager = HDFSManager()
        if hdfs_manager.test_connection():
            files = hdfs_manager.list_files('raw_data')
            print(f"HDFS文件列表: {files}")
        return True
    except Exception as e:
        print(f"❌ HDFS连接测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Ubuntu Python环境测试")
    print("=" * 40)

    test_imports()
    test_hdfs_connection()

    print("\n✅ Python环境测试完成")
EOF

python test_python_env.py
```

### 11.3 创建一键启动脚本
```bash
cat > start_project.sh << 'EOF'
#!/bin/bash

echo "🚀 启动大数据平台项目 (Ubuntu版)"
echo "================================"

# 检查Hadoop集群状态
echo "1. 检查Hadoop集群..."
if ! jps | grep -q "NameNode"; then
    echo "启动Hadoop集群..."
    start-dfs.sh
    start-yarn.sh
    sleep 10
fi

# 激活Python环境
echo "2. 激活Python环境..."
source venv/bin/activate

# 检查数据文件
echo "3. 检查数据文件..."
if ! hdfs dfs -test -e /bigdata-project/raw-data/data.csv; then
    echo "上传数据文件到HDFS..."
    hdfs dfs -mkdir -p /bigdata-project/raw-data
    hdfs dfs -put data.csv /bigdata-project/raw-data/ 2>/dev/null || echo "数据文件已存在"
fi

# 运行数据分析
echo "4. 运行数据分析..."
python analysis/data_analysis.py

# 启动Web服务器
echo "5. 启动Web服务器..."
echo "访问地址:"
echo "  主页: http://localhost:5000"
echo "  大屏1: http://localhost:5000/dashboard1"
echo "  大屏2: http://localhost:5000/dashboard2"
echo "  大屏3: http://localhost:5000/dashboard3"
echo ""
echo "按 Ctrl+C 停止服务器"

python app.py
EOF

chmod +x start_project.sh
```

## 十二、Ubuntu系统优化建议

### 12.1 性能监控
```bash
# 安装系统监控工具
sudo apt install -y htop iotop nethogs

# 创建监控脚本
cat > monitor_system.sh << 'EOF'
#!/bin/bash

echo "📊 Ubuntu系统资源监控"
echo "====================="

echo "CPU和内存使用情况:"
free -h
echo ""

echo "磁盘使用情况:"
df -h
echo ""

echo "网络连接:"
netstat -tlnp | grep -E "(9000|9870|8088)"
echo ""

echo "Hadoop进程:"
jps
EOF

chmod +x monitor_system.sh
```

### 12.2 日志管理
```bash
# 创建日志查看脚本
cat > view_logs.sh << 'EOF'
#!/bin/bash

echo "📋 Hadoop日志查看工具"
echo "==================="

echo "选择要查看的日志:"
echo "1) NameNode日志"
echo "2) DataNode日志"
echo "3) ResourceManager日志"
echo "4) NodeManager日志"
echo "5) 所有错误日志"

read -p "请选择 (1-5): " choice

case $choice in
    1) tail -f $HADOOP_HOME/logs/hadoop-hadoop-namenode-*.log ;;
    2) tail -f $HADOOP_HOME/logs/hadoop-hadoop-datanode-*.log ;;
    3) tail -f $HADOOP_HOME/logs/yarn-hadoop-resourcemanager-*.log ;;
    4) tail -f $HADOOP_HOME/logs/yarn-hadoop-nodemanager-*.log ;;
    5) grep -i error $HADOOP_HOME/logs/*.log | tail -20 ;;
    *) echo "无效选择" ;;
esac
EOF

chmod +x view_logs.sh
```

完成以上步骤后，您的Ubuntu Hadoop集群就完全配置好了！现在可以运行 `./start_project.sh` 来启动整个项目。
